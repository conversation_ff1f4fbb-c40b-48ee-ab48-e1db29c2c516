<?php 

new  sleeper_api;

class sleeper_api {
	protected $slug;
	protected $name;
	protected $endpoint;
	protected $season;
    
	protected $userId;
	protected $leagueTeams;
	protected $rounds;
	protected $includeDraftPicks;
	protected $api_url;
	
	public function __construct(){
		$this->slug = 'sleeper_api';
		$this->name = 'Sleeper';
		$settings = get_option('dtc_settings');	
		$this->endpoint =$settings[''.$this->slug.'_url'];
		$this->season =$settings[''.$this->slug.'_season'];

        $this->api_url =$settings['sleeper_api_url'];

		// Not sure if this is needed. Leaving it for now.
		$this->rounds = 4;

		$this->includeDraftPicks = true;

		$this->leagueTeams = [];

		add_action('dtc/admin/settings',array($this,'settings'));
		add_action('dtc/admin/settings/api_settings',array($this,'api_settings'));
		add_action('dtc_calculator_bottom', array($this,'view'));
		add_action('dtc/league_import/content',array($this,'import_content'));
		add_action('dtc/league_import/button',array($this,'import_button'));

		add_action( 'wp_ajax_dtc_ajax_'.$this->slug.'_get_league_settings', array($this,'ajax_get_league_settings'));	

		add_action( 'wp_ajax_dtc_ajax_'.$this->slug.'_get_opposing_players_dropdown', array($this,'ajax_get_opposing_players_dropdown'));	
		add_action( 'wp_ajax_nopriv_dtc_'.$this->slug.'_get_opposing_players_dropdown', array($this,'ajax_get_opposing_players_dropdown'));

		add_action( 'wp_ajax_dtc_ajax_'.$this->slug.'_get_league_players', array($this,'ajax_get_league_players'));	
		add_action( 'wp_ajax_nopriv_dtc_ajax_'.$this->slug.'_get_league_players', array($this,'ajax_get_league_players'));
		
		add_action( 'wp_ajax_dtc_ajax_'.$this->slug.'_login', array($this,'ajax_login'));	
		add_action( 'wp_ajax_nopriv_dtc_ajax_'.$this->slug.'_login', array($this,'ajax_login'));
		
		add_action( 'wp_ajax_dtc_'.$this->slug.'_ajax_logout', array($this,'ajax_logout'));	
		add_action( 'wp_ajax_nopriv_dtc_'.$this->slug.'_ajax_logout', array($this,'ajax_logout'));

		add_action( 'wp_ajax_dtc_ajax_'.$this->slug.'_main_loader', array($this,'ajax_main_loader'));	
		add_action( 'wp_ajax_nopriv_dtc_ajax_'.$this->slug.'_main_loader', array($this,'ajax_main_loader'));

		add_action('wp_ajax_process_sleeper_login', array($this,'process_sleeper_login'));
		add_action('wp_ajax_nopriv_process_sleeper_login', array($this,'process_sleeper_login'));
	}

	/* Sleeper Basic Integration Functions */

	function ajax_main_loader() {
		global $current_user;
		$user_id = $current_user->ID;
        
		$loginWindow = $this->login();

		if ($loginWindow !== false) {
    		echo $loginWindow;
		}
		else {
			echo '	
			<h1>'.$this->name.'</h1>
			<div class="dtc-mfl-nav">
				<a href="#" class="'.$this->slug.'-refresh-data mfl-button" data-id="'.$current_user->ID.'">Refresh Data</a>
				<a href="#" class="'.$this->slug.'-logout mfl-button" data-id="'.$current_user->ID.'">Logout</a>
			</div>
			';
	
			$leagues = $this->get_leagues();

			echo '<div class="dtc-mfl-half">';
			echo '	<div class="dtc-half-inner">';

			$name = $this->slug . '_your_team';   
			$html = '<select name="'.$name.'" class="'.$name.' league_dropdown"><option value="">Your Team</option>';
		
			foreach ($leagues as $league) {
				$html .= '<option value="'.$league->leagueId.'" data-my-team-id="'.$league->teamId.'" data-my-team-name="'.$league->teamName.'">'.$league->leagueName.' -  '.$league->teamName.'</option>';
			}

			$html .='</select>';

			$name = $this->slug . '_your_team';

			$html .= '
				<script>
					jQuery(document).ready(function(){
						var league_id = localStorage.getItem("dtc_league_id");

						if (league_id) {
							jQuery("select[name=\'' . $name . '\'] option[value=\'" + league_id + "\']").prop("selected", true);
							jQuery("select[name=\'' . $name . '\']").trigger("change");
						}
					});
				</script>
				';

			echo $html;

			echo '
				</div>
			</div>
			';

			echo '
				<div class="dtc-mfl-half">
					<div class="dtc-half-inner">
			';
			echo '<div class="'.$this->slug.'-opposing-teams"><select disabled="disabled" ><option value="">Your Competitor</option></select></div>';
			echo '
				</div>
			</div>
			
			<div style="clear:both"></div>
			
			<div class="mfl-import-calc">
				<a href="#" class="mfl-import-calc-button">Import to Calc</a>
			</div>
			
			<div class="'.$this->slug.'-league-settings"></div>
			
			<div class="mfl-team-gutter">
				<div class="dtc-mfl-half">
					<div class="dtc-half-inner">
						<div class="'.$this->slug.'-import-team-one">
						</div>
					</div>
				</div>
			
				<div class="dtc-mfl-half">
					<div class="dtc-half-inner">
						<div class="'.$this->slug.'-import-team-two">
				
						</div>
					</div>
				</div>
			
				<div style="clear:both">
				</div>
			</div>

			<a href="#" class="mfl-import-calc-button" style="margin-bottom:20px">Import to Calc</a>
			';
		}

		die();
	}

	function ajax_login() {
		$this->request_cookie($_POST[''.$this->slug.'-username']);	
		die();
	}

	function ajax_logout() {
		global $wpdb, $current_user;

		$wpdb->query("DELETE FROM ".$wpdb->prefix . "options WHERE option_name like '%".$this->slug."_user_".$current_user->ID."%'");	
		delete_user_meta($current_user->ID,'_'.$this->slug.'_login_cookie');
		delete_user_meta($current_user->ID,'_'.$this->slug.'_login_name');
	}

	function login() {
		global $wpdb, $current_user;
		
		$login_cookie =  get_user_meta($current_user->ID,'_'.$this->slug.'_login_cookie',true);
		$show_form = false;

		if ($login_cookie == '') {
			$show_form = true;
		}
		
		if ($show_form == true) {
		
			return '
			<div class="dtc-mfl-nav '.$this->slug.'-login">
				<div class="'.$this->slug.'-login-block">
					<form action="" method="post" class="'.$this->slug.'-import-login">
						<div style="text-align:center"><img src="'.DTC_URL.'/asetts/images/api/sleeper.png"></div>
						
						<h1>Sleeper Login</h1>
						<input type="text" class="'.$this->slug.'-username" placeholder="Username"> <input type="submit" name="mfl-login" value="Login" class="dtc-button">
						<p><a href="https://sleeper.app/forgot" target="_blank">Reset Your Password</a> | <a href="https://sleeper.app/create" target="_blank">Create an account</a></p>
					</form>
				</div>
			</div>
			';	
		} else {
			return false;	
		}
	}

	function request_cookie($username, $password= false) {
		global $current_user;
		
		$request = wp_remote_get($this->endpoint.'user/'.$username.'',array());
		$json = $request['body'];
		
		if ($json == 'null') {
			echo '<div class="dtc-error"><p>Invalid '.$this->username.' Username</p></div>';
			echo $this->login();	
		} else {
			$data = json_decode($request['body']);	
			update_user_meta($current_user->ID,'_'.$this->slug.'_login_cookie',$data->user_id);
			update_user_meta($current_user->ID,'_'.$this->slug.'_login_name',$data->username);
			
			echo $this->ajax_main_loader();
		}
	}

	function is_active() {
		$settings = get_option('dtc_settings');	
	
		if ($settings[''.$this->slug.'_display']== 0) {
			return false;
		}

		if ($settings[''.$this->slug.'_display']== 1 && !current_user_can('manage_options') ) {
			return false;
		}
		
		return true;
	}

    function ajax_refresh_user_league() {
		global $wpdb,$current_user;
		
		if($current_user->ID) {
			$wpdb->query("DELETE FROM ".$wpdb->prefix . "options WHERE option_name like '%mfl_user_".$current_user->ID."%'");	
		} else {

		}

		die();	
	}

	function import_content() {
		if ($this->is_active() == true) {

			$this->javascript_functions();
			?>

			<style type="text/css">
				.<?php echo $this->slug; ?>-loader select{width:100%;}
				.<?php echo $this->slug; ?>-loader {color:#c3c6ca;}
				.<?php echo $this->slug; ?>-login{background-color: #01081f;
				color: #fbfbfb;}
				.<?php echo $this->slug; ?>-login{min-height:500px;padding:20px 0px;text-align: center;}
				.<?php echo $this->slug; ?>-login input[type=text]{width:80%;border-radius:10px;}
				.<?php echo $this->slug; ?>-login  .dtc-button{
					-moz-border-radius: 26px;
					border-radius: 26px;
					border: 1px solid #07c5ff;
					height: 52px;
					font-size: 14px;
					color: #00ceb8;
					background-color:transparent;
					text-align:center;
					font-weight:600;
					min-width:100px;
				}
			</style>
			<div class="<?php echo $this->slug; ?>-loader league-import-item">
				
			</div>
			<?php
		}
	}

	function import_button() {
		if ($this->is_active()  == true) {
				echo '<a href="" class="dtc-refresh-main-loader '.$this->slug.'-loader-button" data-id="'.$this->slug.'">'.$this->name.'</a>';
		}
	}

	public function view() {
	}

	function api_settings($settings) {
		echo ' 
		<tr>
			<td style="width:150px !important">'.$this->name.' URL</td>
			<td><input type="text" name="dtc_settings['.$this->slug.'_url]" value="'.$settings[''.$this->slug.'_url'].'" style="width:300px;max-width:none"> </td>
		</tr>
		<tr>
			<td style="width:150px !important">'.$this->name.' Season</td>
			<td><input type="text" name="dtc_settings['.$this->slug.'_season]" value="'.$settings[''.$this->slug.'_season'].'" style="width:300px;max-width:none"> </td>
		</tr>
		';
	}

	function ajax_get_league_settings() {
		global $current_user;
		$user_id = $current_user->ID;

		$league_id = $_POST['league_id'] ?? '';

		if ($league_id == '') {
			die();
		}

		$leagueInfo = $this->get_settings_for_league($league_id);

        $login_cookie =  get_user_meta($user_id,'_'.$this->slug.'_login_cookie',true);
		
		?>
			<script type="text/javascript">
				localStorage.setItem('dtc_integration_name', 'sleeper');
				localStorage.setItem('dtc_league_name', '<?php echo $leagueInfo->league_name; ?>');
				localStorage.setItem('dtc_league_id', '<?php echo $league_id; ?>');
				localStorage.setItem('dtc_integration_token', null);

				// Populating this so we don't have conflicting uses of rotoGPT and DTC
				window.IntegrationName = "sleeper";
				window.IntegrationLeagueName = "<?php echo $leagueInfo->league_name; ?>";
				window.IntegrationLeagueId = "<?php echo $league_id; ?>";

				var event = new Event('IntegrationLeagueNameChanged');
				window.dispatchEvent(event);

				window.rotoGPT_LeagueIntegrationName = "sleeper";
				window.rotoGPT_LeagueUserId = "<?php echo $login_cookie; ?>";
				window.rotoGPT_LeagueId = "<?php echo $league_id; ?>";
				window.rotoGPT_LeagueName = "<?php echo $leagueInfo->league_name; ?>";
			</script>
		<?php

		$rules_array = $this->get_rules_array($leagueInfo);

		echo '	
		<input type="hidden" class="mfl-rule-team-type" value="'.$rules_array['team_type'].'">
		<input type="hidden" class="mfl-rule-team-format" value="'.$rules_array['team_format'].'">
		<input type="hidden" class="mfl-rule-team-size" value="'.$rules_array['team_size'].'">
		<input type="hidden" class="mfl-rule-team-tepre" value="'.$rules_array['tepre'].'">
		<input type="hidden" class="mfl-rule-team-rbppc" value="'.$rules_array['rbppc'].'">';

		echo '
		<div class="mfl-league_settings">
			<span><strong>Type: </strong>'.$rules_array['team_type'].'</span>
			<span><strong>Format: </strong>'.$rules_array['team_format'].'</span>
			<span><strong>Size: </strong>'.$rules_array['team_size'].'tm</span>
			<span><strong>TE Premium? </strong>'.$rules_array['tepre'].'</span>
			<span><strong>RB PPC? </strong>'.$rules_array['rbppc'].'</span>
		</div>';

		?>
			<script type="text/javascript">
				_dtc_save_setting('ls', "<?php echo $rules_array['team_size']; ?>");

				if ("<?php echo $rules_array['team_type']; ?>" == "PPR") {
					_dtc_save_setting('lt', "ppr");
				} else if ("<?php echo $rules_array['team_type']; ?>" == "HALF PPR") {
					_dtc_save_setting('lt', "half_ppr");
				} else if ("<?php echo $rules_array['team_type']; ?>" == "NON PPR") {
					_dtc_save_setting('lt', "nonppr");
				}
				
				_dtc_save_setting('lf', "<?php echo strtolower($rules_array['team_format']); ?>");

				var lfa_settings = '';

				if ("<?php echo $rules_array['tepre']; ?>" == "YES") {
					lfa_settings += 'te-premium,';
				}

				if ("<?php echo $rules_array['rbppc']; ?>" == "YES") {
					lfa_settings += 'rb-ppc-premium,';
				}

				lfa_settings += 'offense,';

				_dtc_save_setting('lfa',lfa_settings);

				recalculatePlayers();
			</script>
		<?php

		die();
	}

	function get_rules_array($leagueInfo) {
		$rules_array = ! empty( $rules_array ) ? $rules_array : [];

		$rules_array['team_type'] = $leagueInfo->league_format;

		// Access settings as array since getLeagueSettings returns an array
		$settings = $leagueInfo->settings;

		if (!empty($settings['superflex']) && $settings['superflex']) {
			$rules_array['team_format'] = 'SF';
		} else if (!empty($settings['2QB']) && $settings['2QB']) {
			$rules_array['team_format'] = '2QB';
		} else {
			$rules_array['team_format'] = 'STANDARD';
		}

		$rules_array['team_size'] = $leagueInfo->league_size ?? '';

		if (!empty($settings['TE_Premium']) && $settings['TE_Premium']) {
			$rules_array['tepre'] = 'YES';
		} else {
			$rules_array['tepre'] = 'NO';
		}

		if (!empty($settings['RB_PPC']) && $settings['RB_PPC']) {
			$rules_array['rbppc'] = 'YES';
		} else {
			$rules_array['rbppc'] = 'NO';
		}

		$rules_array['devy'] = 'NO';

		return $rules_array;
	}

	function ajax_get_opposing_players_dropdown() {
		global $current_user;
		
		$league_id = $_POST['league_id'] ?? '';
		$team_id = $_POST['my_team_id'] ?? '';
		
		$class_name = $this->slug . '_load_opposing_team';

		$teams = $this->get_opponents($league_id,$team_id);	
		
		if (!empty($teams)) {
			$firstTeam = $teams[0];
		
			$leagueId = $firstTeam->leagueId;
			$leagueName = $firstTeam->leagueName;

			$league_name = ! empty( $leagueName ) ? $leagueName : '';
			$html = '<select name="'.$class_name.'" class="'.$class_name.'" data-id="'.$leagueId.'" data-name="'.$league_name.'"><option value="">Your Competitor</option>';
			
			foreach($teams as $team) {
				if ($team->teamId !== $team_id) {
					$html .= '<option value="'.$team->teamId.'" data-my-team-name="'.$team->teamName.'" >'.$team->teamName.'</option>';				
				}
			}
			
			$html .='</select>';
			
			echo $html;
		}

		die();
	}

	function settings($settings) {
		$selected0 = '';
		$selected1 = '';
		$selected2 = '';

		echo '  
		<tr>
			<td style="width:150px">'.$this->name.' Display</td>
			<td>';
			if ($settings[''.$this->slug.'_display'] == 0) {
				$selected0 ='selected="selected"'; 
			}

			if ($settings[''.$this->slug.'_display'] == 1) {
				$selected1 ='selected="selected"'; 
			}

			if ($settings[''.$this->slug.'_display'] == 2) {
				$selected2 ='selected="selected"'; 
			}

			echo ' <select name="dtc_settings['.$this->slug.'_display]"><option value="0" '.$selected0.'>Nobody</option><option value="1" '.$selected1.'>Admins</option><option value="2" '.$selected2.'>Everyone</option></select></td>
		</tr>
		';
	}

	function restrict_pages() {
		global $current_user, $post;
			
		$restrict_pages = array(30349);
		
		if (in_array($post->ID,$restrict_pages ) && !is_admin() && is_user_logged_in()  == false) {
				wp_redirect('/login/?redirect=/import-team/');	
		}
	}

	function ajax_get_league_players() {
		global $wpdb, $current_user;
		
		$league_id = $_POST['league_id'] ?? '';
		$team_id = $_POST['my_team_id'] ?? '';

        if (empty($league_id) || empty($team_id)) {
            die();
        }

		$plugin_base_url = plugin_dir_url(__DIR__);
		echo'
		<table class="mfl-trade-table">
			<thead>
				<tr>
					<th style="min-width:24px; padding-top:2px;"><img style="width:24px" src="' . $plugin_base_url . '/asetts/images/league-import-trade.svg" title="TRADE"></t>
					<th style="min-width:24px; padding-top:2px; text-align:left"><img style="width:24px" src="' . $plugin_base_url . '/asetts/images/league-import-player.svg" title="PLAYER"></t>
					<th style="min-width:24px; padding-top:2px;"><img style="width:24px" src="' . $plugin_base_url . '/asetts/images/league-import-position.svg" title="POSITION"></th>
					<th style="min-width:24px; padding-top:2px;"><img style="width:24px" src="' . $plugin_base_url . '/asetts/images/league-import-value.svg" title="DTC VALUE"></th>
				</tr>
			</thead>
		<tbody>
		';

		$leagueInfo = $this->get_settings_for_league($league_id);
		$rules_array = $this->get_rules_array($leagueInfo);

		$roster = $this->get_team_roster($league_id, $team_id);

		$positionOrder = [
			'QB' => 1,
			'RB' => 2,
			'WR' => 3,
			'TE' => 4,
			'DE' => 5,
			'DT' => 6,
			'LB' => 7,
			'CB' => 8,
			'S'  => 9,
		];

		$player_infos = [];

		foreach ($roster as $player_o) {
			$player_info = $this->get_player($player_o);
			if (empty($player_info) || $player_info['position'] === 'DEF') {
				continue;
			}

			$player_info['mfl_id'] = $player_info['mfl_id'] ?? '';
			$player_infos[] = $player_info;
		}

		usort($player_infos, function ($a, $b) use ($positionOrder) {
			$posA = $positionOrder[$a['position']] ?? PHP_INT_MAX;
			$posB = $positionOrder[$b['position']] ?? PHP_INT_MAX;
			return $posA <=> $posB;
		});

		foreach ($player_infos as $player_info) {
			echo '<tr>';
			echo '<td style="text-align:center"><input type="checkbox" data-id="player" name="trade-' . sanitize_text_field( $_POST['side'] ) . '[]" class="mfl-trade-object" data-side="' . sanitize_text_field( $_POST['side'] ) . '" value="' . $player_info['id'] . '" data-nonppr="' . $player_info['nonppr'] . '" data-type="' . $player_info['type'] . '"></td>';
			echo '<td class="mfl-trade-table-player-team" style="text-align:left; padding-left:5px; padding-right:5px;"><span class="mfl-trade-table-player">' . stripslashes($player_info['name']) . '</span> <span class="mfl-trade-table-team">' . $player_info['team'] . '</span></td>';
			
			echo '<td style="text-align:center; padding-left:3px; padding-right:3px;">';
			if ($player_info['position'] === 'TE') {
				echo '<span style="display:none">Z</span>';
			}
			echo $player_info['position'];
			echo '</td>';
			
			echo '<td style="text-align:center">' . dtc_get_player_total($player_info['mfl_id'], $rules_array, true) . '</td>';
			echo '</tr>';
		}

		$season = date("Y");

		if ($this->includeDraftPicks) {
			$draft_picks = $this->get_team_picks($league_id, $team_id);

			foreach ( $draft_picks as $pick ) {
				$pickTwoDigits = str_pad( $pick['pick'], 2, '0', STR_PAD_LEFT );

				$pick_info = $pick['year'] . ' ' . $pick['round'] . '.' . $pickTwoDigits;

				$pick_data = $this->get_pick( $pick_info, $pick['year'], $pick['round'] );

				if ( ! empty( $pick_data ) ) {
					$fields = [
						'nonppr','ten','twelve','fourteen',
						'sixteen','tensf','twelvesf',
						'fourteensf','sixteensf','id'
					];
					foreach ( $fields as $field ) {
						if ( ! isset( $pick_data[ $field ] ) ) {
							$pick_data[ $field ] = '';
						}
					}

					// render the row
					echo sprintf( '
					<tr>
						<td style="text-align:center">
							<input
								type="checkbox"
								data-id="pick"
								class="mfl-trade-object trade-%s"
								data-side="%s"
								data-type=""
								data-year="%s"
								data-round="%s"
								data-pick="%s"
								data-nonppr="%s"
								data-ten="%s"
								data-twelve="%s"
								data-fourteen="%s"
								data-sixteen="%s"
								data-tensf="%s"
								data-twelvesf="%s"
								data-fourteensf="%s"
								data-sixteensf="%s"
								value="%s"
							>
						</td>
						<td style="text-align:left; padding:0 5px;">%s</td>
						<td style="font-size:0px;">ZZ</td>
						<td style="text-align:center">%s</td>
					</tr>',
						htmlspecialchars( $_POST['side'], ENT_QUOTES, 'UTF-8' ),
						htmlspecialchars( $_POST['side'], ENT_QUOTES, 'UTF-8' ),
						htmlspecialchars( $pick['year'], ENT_QUOTES, 'UTF-8' ),
						htmlspecialchars( $pick['round'], ENT_QUOTES, 'UTF-8' ),
						htmlspecialchars( $pickTwoDigits, ENT_QUOTES, 'UTF-8' ),
						htmlspecialchars( $pick_data['nonppr'],    ENT_QUOTES, 'UTF-8' ),
						htmlspecialchars( $pick_data['ten'],       ENT_QUOTES, 'UTF-8' ),
						htmlspecialchars( $pick_data['twelve'],    ENT_QUOTES, 'UTF-8' ),
						htmlspecialchars( $pick_data['fourteen'],  ENT_QUOTES, 'UTF-8' ),
						htmlspecialchars( $pick_data['sixteen'],   ENT_QUOTES, 'UTF-8' ),
						htmlspecialchars( $pick_data['tensf'],     ENT_QUOTES, 'UTF-8' ),
						htmlspecialchars( $pick_data['twelvesf'],  ENT_QUOTES, 'UTF-8' ),
						htmlspecialchars( $pick_data['fourteensf'],ENT_QUOTES, 'UTF-8' ),
						htmlspecialchars( $pick_data['sixteensf'], ENT_QUOTES, 'UTF-8' ),
						htmlspecialchars( $pick_data['id'], ENT_QUOTES, 'UTF-8' ),
						htmlspecialchars( $pick_data['pick'] . ' ' . $pick['description'], ENT_QUOTES, 'UTF-8' ),
						htmlspecialchars(
							dtc_get_player_total(
								0,
								$rules_array,
								true,
								'draft_picks',
								$pick_data['pick']
							),
							ENT_QUOTES,
							'UTF-8'
						)
					);
				}
			}
		}
		
		echo '</tbody></table>';
		die();
	}

	function fix_name($name) {
		$name = str_replace("","%",$name);
		$name = str_replace(" Jr.","%",$name);
		$name = str_replace(" Sr.","%",$name);
		$name = str_replace("'","%",$name);
		$name = str_replace('"','%',$name);
		$name = str_replace(".",'%',$name);
		$name = str_replace("-",'%',$name);

		return $name;
	}

	function get_player($player) {
		global $wpdb;
		
		$query = $wpdb->prepare("SELECT * FROM (SELECT * FROM " . $wpdb->prefix . "dtc_players UNION SELECT ALL * FROM " . $wpdb->prefix . "dtc_players_idp) as t where sleeper_id = %s", $player->player_id);

		$r = $wpdb->get_results($query, ARRAY_A);
		
		if ($r) {
			return $r[0];
		} else {
			return false;	
		}
	}

	function get_pick($pick_id, $year, $round) {
		global $wpdb;
	
		// Try to get the exact pick
		$r = $wpdb->get_results($wpdb->prepare("SELECT * FROM wp_dtc_draft_picks WHERE pick = %s", $pick_id), ARRAY_A);

		if (!empty($r)) {
			return $r[0]; // Return the exact match
		}
	
		// Construct the "Mid" value
		$mid_pick = sprintf("%s %s (Mid)", $year, $this->ordinal($round));

		// Try to find the mid pick
		$r = $wpdb->get_results($wpdb->prepare("SELECT * FROM wp_dtc_draft_picks WHERE pick = %s", $mid_pick), ARRAY_A);
		
		if (!empty($r)) {
			return $r[0];
		}
	
		return [];
	}

	function javascript_functions() {
	?>
		<script type="text/javascript">
			function dtc_refresh_sleeper_loader() {
				add_dtc_mfl_create_overlay(".mfl-loader");
				jQuery(".league-import-item").hide();
		
				jQuery.post(dtc.ajaxurl, {'action':'dtc_ajax_<?php echo $this->slug; ?>_main_loader'},
					function(response) {					
						jQuery(".<?php echo $this->slug; ?>-loader").html(response);
						jQuery(".<?php echo $this->slug; ?>-loader").show();
				});
			}

			function dtc_<?php echo $this->slug; ?>_load_team(league_id,franchise_id,team_name,location,side){
				add_dtc_mfl_create_overlay(location);
				jQuery.post(dtc.ajaxurl, {'action':'dtc_ajax_<?php echo $this->slug; ?>_get_league_players','side':side, 'league_id':league_id,'my_team_id':franchise_id,'my_team_name':team_name}, function(response) {					
						jQuery(location).html(response);
				});
			}

			jQuery(document).ready(function($) {
				$( document ).on( "submit", ".<?php echo $this->slug; ?>-import-login",
					function() {
						$(".dtc-error").remove();
						let sbutmitButton = $(this).find('input[type="submit"]');
						let currentText = sbutmitButton.val();
						sbutmitButton.val('Logging In...');
						sbutmitButton.attr('disabled', true);

						$.post(dtc.ajaxurl, {'action':'dtc_ajax_<?php echo $this->slug; ?>_login', '<?php echo $this->slug; ?>-username':$(".<?php echo $this->slug; ?>-username").val()},
							function(response) {					
								jQuery(".<?php echo $this->slug; ?>-loader").html(response);
								dtc_ga_track('<?php echo $this->name; ?>','User Login');
								sbutmitButton.val(currentText);
								sbutmitButton.attr('disabled', false);
							});

						return false;
					}
				);
			
				$( document ).on( "click", ".<?php echo $this->slug; ?>-logout",
					function() {
						let sbutmitButton = $(this);
						let currentText = sbutmitButton.text();
						sbutmitButton.text('Logging Out...');
						sbutmitButton.attr('disabled', true);
						
						$.post(dtc.ajaxurl, {'action':'dtc_<?php echo $this->slug; ?>_ajax_logout', 'user_id':$(this).attr('data-id')},
							function(response) {
								// Making sure to clear out the values on logout
								localStorage.removeItem('dtc_league_name');
								localStorage.removeItem('dtc_league_id');

								// Ensure we stay on the Sleeper tab after logout
								localStorage.setItem('dtc_integration_name', 'sleeper');

								window.IntegrationLeagueName = undefined;
								window.IntegrationLeagueId = undefined;

								window.rotoGPT_LeagueToken = undefined;
								window.rotoGPT_LeagueId = undefined;
								window.rotoGPT_LeagueName = undefined;
								window.rotoGPT_UserEmail = undefined;

								var event = new Event('IntegrationLeagueNameChanged');
								window.dispatchEvent(event);

								dtc_refresh_main_loader();
								dtc_ga_track('<?php echo $this->name; ?>','User Logout');
								sbutmitButton.text(currentText);
								sbutmitButton.attr('disabled', false);
							});

						return false;		
					}
				);

				$( document ).on( "click", ".<?php echo $this->slug; ?>-refresh-data",
					function() {
						$.post(dtc.ajaxurl, {'action':'dtc_<?php echo $this->slug; ?>_ajax_refresh_data', 'user_id':$(this).attr('data-id')},
							function(response) {					
								dtc_refresh_main_loader();
								alert("Refreshed League Data");
								dtc_ga_track('<?php echo $this->name; ?>','Refresh Data');
							});
				
						return false;
					}
				);

				$( document ).on( "change", ".<?php echo $this->slug; ?>_load_opposing_team", function() {
					dtc_<?php echo $this->slug; ?>_load_team(jQuery(this).attr('data-id'),jQuery(this).val(),jQuery('option:selected', this).attr('data-my-team-name'),".<?php echo $this->slug; ?>-import-team-two","right");	
				});

				$( document ).on( "change", ".<?php echo $this->slug; ?>_your_team", function() {
					$(".<?php echo $this->slug; ?>-import-team-two").empty();
					$(".<?php echo $this->slug; ?>-import-team-one").empty();	
					$(".<?php echo $this->slug; ?>_load_opposing_team").val("");
			
					/* Clear these incase they changed */
					localStorage.setItem('dtc_integration_name', 'sleeper');
					window.IntegrationName = "sleeper";
					window.rotoGPT_LeagueIntegrationName = "sleeper";
					window.rotoGPT_LeagueUserId = undefined;
					window.rotoGPT_LeagueToken = undefined;
					window.rotoGPT_LeagueId = undefined;
					window.rotoGPT_LeagueName = undefined;
					window.rotoGPT_UserEmail = undefined;

					$.post(dtc.ajaxurl, {'action':'dtc_ajax_<?php echo $this->slug; ?>_get_league_settings', 'league_id':$(this).val(),'my_team_id':$('option:selected', this).attr('data-my-team-id')}, function(response) {					
						$(".<?php echo $this->slug; ?>-league-settings").html(response);

						dtc_ga_track('<?php echo $this->name; ?>','League Settings');					
					});
			
					dtc_<?php echo $this->slug; ?>_load_team(jQuery(this).val(),jQuery('option:selected', this).attr('data-my-team-id'),jQuery('option:selected', this).attr('data-my-team-name'),".<?php echo $this->slug; ?>-import-team-one","left");
				
					$.post(dtc.ajaxurl, {'action':'dtc_ajax_<?php echo $this->slug; ?>_get_opposing_players_dropdown', 'league_id':$(this).val(),'my_team_id':$('option:selected', this).attr('data-my-team-id'),'my_team_name':$('option:selected', this).attr('data-my-team-name')}, function(response) {					
						$(".<?php echo $this->slug; ?>-opposing-teams").html(response);
					});
				});
			});
		</script>
	<?php
	}


	/****************************************************************************************
	 * 
	 * 
	 *              Sleeper Data Manipulation Functions
	 *              (Mapping it to what we need for DTC)
	 * 
	 * 
	 ****************************************************************************************/
    public function get_leagues()
    {
        global $current_user;
        $user_id = $current_user->ID;

        $leagues = [];

        $login_cookie =  get_user_meta($user_id,'_'.$this->slug.'_login_cookie',true);

        if (empty($login_cookie)) {
            return $leagues;
        }

        $leagues_data = $this->getAllLeaguesForUser($login_cookie);

        if (!$leagues_data) {
            return $leagues;
        }

        foreach ($leagues_data as $league) {
            $leagueId = $league->league_id ?? null;
            $leagueName = $league->name ?? null;
            
            $teams = $this->getTeamUsers($leagueId);

            if ($teams) {
                foreach($teams as $team) {
					if ($team->user_id == $login_cookie) {
                        $teamId = $login_cookie;
                        $teamName = isset($team->metadata) && isset($team->metadata->team_name) ? $team->metadata->team_name : $team->display_name;
            
                        $sleeperLeagueTypeId = null;
                        $sleeperLeagueType = null;
            
                        $leagueTeam = new stdClass();
                        $leagueTeam->leagueId = $leagueId;
                        $leagueTeam->leagueName = $leagueName;
                        $leagueTeam->teamId = $teamId;
                        $leagueTeam->teamName = $teamName;
                        $leagueTeam->sleeperLeagueTypeId = $sleeperLeagueTypeId;
                        $leagueTeam->sleeperLeagueType = $sleeperLeagueType;
            
                        $leagues[] = $leagueTeam;
                    }
                }
            }
        }

        return $leagues;
    }


    public function get_settings_for_league($league_id) {
		global $current_user;
		$user_id = $current_user->ID;
		$login_cookie =  get_user_meta($current_user->ID,'_'.$this->slug.'_login_cookie',true);
		
		$leagues_data = $this->getAllLeaguesForUser($login_cookie);
		if (!$leagues_data) {
			return null;
		}
		
		$foundLeague = null;
		foreach ($leagues_data as $league) {
			$leagueId = $league->league_id ?? null;
			if ($leagueId === $league_id) {
				$foundLeague = $league;
				break;
			}
		}
		
		if (!$foundLeague) {
			return null;
		}
		
		$leagueName = $foundLeague->name ?? '';
	
		$leagueInfo = $this->getLeagueInfo($league_id);
		if (!$leagueInfo) {
			return null;
		}
		
    	$league_size = $leagueInfo->settings->num_teams ?? 0;
		
        $scoring_settings = ! empty( $leagueInfo->scoring_settings ) ? $leagueInfo->scoring_settings : '';

		$leagueFormat = $this->checkIfLeagueIsPPR($scoring_settings);
		$settings     = $this->getLeagueSettings($leagueInfo);
		$positions    = $this->getRosterPositions($leagueInfo);
		
		// Draft start time is not available.
		$draft_start_time = null;
		
		// Retrieve the league status.
		$leagueStatus = $leagueInfo->status;
		
		// Assemble and return an object with all the league settings.
		$leagueData = new stdClass();
		$leagueData->league_id        = $league_id;
		$leagueData->league_name      = $leagueName;
		$leagueData->league_size      = (int)$league_size;
		$leagueData->league_format    = $leagueFormat;
		$leagueData->settings         = $settings;
		$leagueData->draft_date       = $draft_start_time;
		$leagueData->league_status    = $leagueStatus;
		$leagueData->roster_positions = $positions;
		
		return $leagueData;
	}


    public function get_opponents($leagueId, $myTeamId): array
	{
		global $current_user;
		$login_cookie = get_user_meta($current_user->ID, '_'.$this->slug.'_login_cookie', true);

		$opponents = [];

		// pull down all the users in this league
		$leagueInfo = $this->getLeagueInfo($leagueId);
		$teams      = $this->getTeamUsers($leagueId);

		if (!empty($teams)) {
			foreach ($teams as $team) {
				if ($team->user_id !== $login_cookie) {
					$teamId   = $team->user_id;
					$teamName = !empty($team->metadata->team_name)
								? $team->metadata->team_name
								: $team->display_name;

					$opponent = new \stdClass();
					$opponent->leagueId   = $leagueId;
					$opponent->leagueName = $leagueInfo->league_name;
					$opponent->teamId     = $teamId;
					$opponent->teamName   = $teamName;

					$opponents[] = $opponent;
				}
			}
		}

		return $opponents;
	}
    

    public function get_team_roster(string $leagueId, string $userId): array {
		$roster = [];
		$rosters = $this->getTeamRosters($leagueId);

		foreach ($rosters as $item) {
			if ((string)$item->owner_id === (string)$userId) {
				foreach ($item->players as $playerId) {
					$currentPlayer = new stdClass();
					$currentPlayer->player_id = $playerId;
					$roster[] = $currentPlayer;
				}
				break; // once we found the matching owner, stop
			}
		}

		return $roster;
	}


	public function get_team_picks( string $league_id, string $team_id ): array {

		global $current_user;
		$user_id    = $current_user->ID;
		$cache_key  = '_'.$this->slug.'_user_'.$user_id.'_team_picks_'.$team_id;
		$user_draft = [];

		$team_picks = [];

		$league     = $this->getLeagueInfo( $league_id );

		$roster_map = $this->get_roster_map( $league_id );

		$drafts = $this->getDrafts( $league_id ); 

		if ( ! empty( $drafts ) && is_array( $drafts ) ) {
			foreach ( $drafts as $draftSummary ) {

				$draft = $this->getDraftResults( $draftSummary->draft_id );

				// Skip completed drafts
				if ( $draft->status !== 'complete' ) {
					$user_draft += $this->get_team_picks_for_year( $draft, $draft->season, $league, $roster_map );
				}

				if ( $league->settings->type != 0 ) {
					$user_draft += $this->get_team_picks_for_year( $draft, $draft->season + 1, $league, $roster_map );
					$user_draft += $this->get_team_picks_for_year( $draft, $draft->season + 2, $league, $roster_map );
				}
			}

			foreach ($user_draft as $draft_year => $draft) {
				
				if (empty($draft['order'])) {
					continue;
				}

				$rosters = $this->getTeamRosters($league_id);
				$team_owner_id = '';
				$roster_id = '';

				if ( ! empty( $league ) ) {
					foreach($rosters as $team){
						// if($team->owner_id == $my_team_id){
						if($team->owner_id == $team_id || (is_array($team->co_owners) && in_array($team_id, $team->co_owners))){
							$team_owner_id = $team->owner_id;
							$roster_id = $team->roster_id;
							$players = $team->players;
							$owned_league = $team;
						}
					}
				}



				foreach ($draft['order'] as $round_no => $round) {
					$i = 0; // fallback counter for “pick 0” entries
					foreach ($round as $pick_key => $pick) {

						// match on owner_id
						if ($pick['owner_id'] != $roster_id) {
							continue;
						}

						// if this pick was traded, look up the human‐readable previous owner
						$previous_owner_text = '';
						if (!empty($pick['previous_owner_id'])) {
							$prev_owner_raw = $this->find_owner_id_by_roster_id(
								$pick['previous_owner_id'],
								$rosters_data
							);
							if ($prev_owner_raw) {
								$prev_user = $this->getUserById($prev_owner_raw);
								$previous_owner_text = ' from ' . ($prev_user->display_name ?? 'Unknown');
							}
						}

						$pick_num = str_pad($pick['pick'], 2, '0', STR_PAD_LEFT);

						// handle “pick == 0” (pre-draft) to keep keys unique
						if ($pick['pick'] == 0) {
							$key = $i++;
						} else {
							$key = $pick['pick'];
						}

						// build your output array
						$team_picks["{$draft_year}.{$pick['round']}.{$key}"] = [
							'year'        => $draft_year,
							'round'       => $pick['round'],
							'pick'        => $pick_num,
							'description' => " Draft Pick {$previous_owner_text}"
						];
					}
				}
			}

			// sort chronologically
			ksort($team_picks);
		}

		return $team_picks;
	}

	function get_team_picks_for_year($draft, $year, $league, $roster_map) {
		$user_draft = array();

		$year = (int)$year;

		$reverse_order = array();
		// $user_draft[ $draft->season]['info'] = $draft;
		$draft_rounds = $draft->settings->rounds;

		// We check the amount of rounds and if needed then we get the rounds from the league not the draft
		$currentYear = date("Y");
		
		if ($draft_rounds > 10 && $year != $currentYear) {
			$draft_rounds = $league->settings->draft_rounds;
		}

		$teams = $draft->settings->teams;
		$pre_draft = false;

		$league_ranks = $this->get_standings($league);
		$draft_order = $this->get_draft_order($draft, $league, $league_ranks);

		if (($league->previous_league_id == '') && ($draft->status !== 'complete')) {
			$pre_draft = true;
		}

		// if($draft_rounds < 10) {
		for ($x = 1; $x <= $draft_rounds; $x++) {
			foreach ($draft_order as $item) {
				if ($pre_draft == true) {
					$pick_text = '';
				} else {
					$pick_text = $item['position'];	
				}

				$user_draft[$year]['order'][$x][$item['position']] =  array('year'=>$year,'round'=>$x,'owner_id'=> $item['roster_id'],'previous_owner_id'=>'','pick'=>$pick_text);
			}		
		}

		// Handle traded picks
		$all_traded_picks = $this->getTradedDraftPicks($league->league_id);

		// Filter picks by year
		$traded_picks = array();

		foreach ($all_traded_picks as $pick) {
			if ((int)$pick->season === (int)$year) {
				$traded_picks[] = $pick;
			}
		}

		foreach($traded_picks as $traded) {
			if ($traded->round <= $draft_rounds) {
				# unset( $user_draft[$traded->season]['order'][$traded->round][$pick]);
				$position = 0;
				
				foreach ($draft_order as $item) {
					if ($item['roster_id'] === $traded->roster_id) {
						$position = $item['position'];
					}
				}

				if ($pre_draft == true && $position != 0) {
					$pick_text = '0';
				} else {
					$pick_text = $position;	
				}

				$user_draft[$year]['order'][$traded->round][$position] =  array('year'=>$year,'round'=>$traded->round,'owner_id'=>$traded->owner_id,'previous_owner_id'=>$traded->roster_id,'pick'=>$pick_text);
			}
		}
		// }

		return $user_draft;
	}

	function get_draft_order($draft, $league, $league_ranks) {
		$draft_order = [];

		try {
			$use_slot_to_roster_id = false;

			if (!empty($draft->slot_to_roster_id)) {			
				foreach ($draft->slot_to_roster_id as $key => $value) {
					if ($key != $value) {
						$allMatch = false;
						break;
					}
				}

				if ($allMatch === false) {
					$use_slot_to_roster_id = true;
				}
			}

			$team_count = $league->settings->num_teams;
			$draft_slot_count = count((array)$draft->slot_to_roster_id);

			if ($team_count !== $draft_slot_count) {
				$use_slot_to_roster_id = false;
			}

			if ($use_slot_to_roster_id) {
				foreach ($draft->slot_to_roster_id as $slot => $roster_id) {
					$owner_record = $this->find_owner_by_roster_id($roster_id, $league_ranks);
					if ($owner_record === null) {
						$draft_order[] = [
							'owner_id' => null,
							'roster_id' => $roster_id,
							'position' => (int)$slot
						];
					} else {
						$draft_order[] = [
							'owner_id' => $owner_record['manager_id'],
							'roster_id' => $roster_id,
							'position' => (int)$slot
						];
					}
				}
			} else {
				foreach ($league_ranks as $team_league_ranking) {
					$draft_spot = $league->total_rosters - ($team_league_ranking['current_rank'] - 1);
					
					$draft_order[] = [
						'owner_id' => $team_league_ranking['manager_id'],
						'roster_id' => $team_league_ranking['roster_id'],
						'position' => $draft_spot
					];
				}
			}

			return $draft_order;
		} catch (Exception $e) {
			return null;
		}
	}

    public function get_standings($league): ?array
    {
        try {
            $rosters_data = $this->getTeamRosters($league->league_id); // Retrieve the roster data for teams in the league

            $league_ranks = [];

            if ($league->status === "in_season") {
                // Calculate the standings for teams that are currently in season
                foreach ($rosters_data as $roster) {
                    $manager_id = $roster->owner_id;
                    $roster_id = $roster->roster_id;

                    // Default values for standings
                    $wins = (int) ($roster->settings->wins ?? 0);
                    $losses = (int) ($roster->settings->losses ?? 0);
                    $fpts = (int) ($roster->settings->fpts ?? 0);
                    $fpts_against = (int) ($roster->settings->fpts_against ?? 0);

                    $league_ranks[] = [
                        'manager_id' => $manager_id,
                        'roster_id' => $roster_id,
                        'wins' => $wins,
                        'losses' => $losses,
                        'fantasy_points_for' => $fpts,
                        'fantasy_points_against' => $fpts_against,
                    ];
                }
            }

            // If the league is complete, post-season, or in pre-draft state
            if (in_array($league->status, ["complete", "drafting", "post_season", "pre_draft"])) {
                $previous_rosters_data = [];

                if ($league->previous_league_id) {
                    // If there is a previous league, fetch that data as well
                    $previous_rosters_data = $this->getTeamRosters($league->previous_league_id);
                }

                // Iterate through each roster and calculate the standings
                foreach ($rosters_data as $roster) {
                    $manager_id = $roster->owner_id;
                    $roster_id = $roster->roster_id;

                    // Default values
                    $wins = (int) ($roster->settings->wins ?? 0);
                    $losses = (int) ($roster->settings->losses ?? 0);
                    $fpts = (int) ($roster->settings->fpts ?? 0);
                    $fpts_against = (int) ($roster->settings->fpts_against ?? 0);

                    // If the current roster has no results, fetch data from the previous season
                    if ($wins === 0 && $losses === 0) {
                        $previous_team_data = $this->find_previous_roster_data($previous_rosters_data, $roster->owner_id);
                        if ($previous_team_data) {
                            $wins = $previous_team_data['wins'] ?? 0;
                            $losses = $previous_team_data['losses'] ?? 0;
                            $fpts = $previous_team_data['fpts'] ?? 0;
                            $fpts_against = $previous_team_data['fpts_against'] ?? 0;
                        }
                    }

                    $league_ranks[] = [
                        'manager_id' => $manager_id,
                        'roster_id' => $roster_id,
                        'wins' => $wins,
                        'losses' => $losses,
                        'fantasy_points_for' => $fpts,
                        'fantasy_points_against' => $fpts_against,
                    ];
                }
            }

            // Rank teams based on wins and fantasy points
            return $this->calculate_rank($league_ranks);

        } catch (Exception $e) {
            // Return null in case of any exception
            return null;
        }
    }

    function calculate_rank(&$league_ranks) {
		usort($league_ranks, [ $this, 'customSort' ]);
		
		foreach ($league_ranks as $i => &$franchise) {
			$franchise['current_rank'] = $i + 1;
		}
		
		return $league_ranks;
	}

	function find_previous_roster_data($previous_rosters_data, $user_id) {
		/**
		 * Retrieves the wins, losses, and fantasy points for a specific user from the previous season's roster data.
		 *
		 * @param string $previous_rosters_data The unique identifier for the user whose previous roster data is to be fetched.
		 * @param string $user_id The unique identifier for the user whose previous roster data is to be fetched.
		 * @return array|null A dictionary containing wins, losses, and fantasy points if found, otherwise None.
		 */

		// Iterate over previous rosters to find matching user data
		foreach ($previous_rosters_data as $roster) {
			if ($roster->owner_id == $user_id) {
				$wins = 0;
				$losses = 0;
				$fpts = 0;
				$fpts_against = 0;

				if (isset($roster->settings->wins) && $roster->settings->wins !== null) {
					$wins = $roster->settings->wins;
				}

				if (isset($roster->settings->losses) && $roster->settings->losses !== null) {
					$losses = $roster->settings->losses;
				}

				if (isset($roster->settings->fpts) && $roster->settings->fpts !== null) {
					$fpts = $roster->settings->fpts;
				}

				if (isset($roster->settings->fpts_against) && $roster->settings->fpts_against !== null) {
					$fpts_against = $roster->settings->fpts_against;
				}

				return [
					'wins' => $wins,
					'losses' => $losses,
					'fpts' => $fpts,
					'fpts_against' => $fpts_against
				];
			}
		}

		return null;
	}

	function customSort($a, $b) {
		if ($a['wins'] == $b['wins']) {
			return ($a['fantasy_points_for'] < $b['fantasy_points_for']) ? -1 : 1;
		}

		return ($a['wins'] < $b['wins']) ? 1 : -1;
	}

	function find_owner_by_roster_id($roster_id, $league_ranks) {
		foreach ($league_ranks as $item) {
			if ($item['roster_id'] === $roster_id) {
				return $item;
			}
		}

		return null;
	}

	function find_owner_id_by_roster_id($roster_id, $rosters_data) {
		foreach ($rosters_data as $item) {
			if ($item->roster_id === $roster_id) {
				return $item->owner_id;
			}
		}

		return null;
	}

	function find_roster_id_by_owner($owner_id, $rosters_data) {
		foreach ($rosters_data as $item) {
			if ($item->owner_id === $owner_id) {
				return $item->roster_id;
			}
		}

		return null;
	}

    public function getLeagueSettings($league_settings): array
    {
		$twoqb = 0;
    	$sf    = 0;

        $scoring_settings = ! empty( $league_settings->scoring_settings ) ? $league_settings->scoring_settings : '';

        $settings = [
            "superflex" => false,
            "2QB" => false,
            "standard" => false,
            "TE_Premium" => false,
            "RB_PPC" => false
        ];

        if ( ! empty( $scoring_settings->bonus_rec_te ) ) {
			if ($scoring_settings->bonus_rec_te >0) {
				$settings["TE_Premium"] = true;
			}
		}

		if ( ! empty( $scoring_settings->rush_att ) && $scoring_settings->rush_att) {
			if ($scoring_settings->rush_att>0) {
				$settings["RB_PPC"] = true;
			}
		}

		if ( ! empty( $league_settings->roster_positions ) ) {
			foreach($league_settings->roster_positions as $pos){
				if ($pos == 'QB') {
					$twoqb +=1;	
				}
		
				if ($pos == 'SUPER_FLEX') {
					$sf +=1;	
				}
			}
		}

		if ($twoqb >1) {
			$settings["2QB"] = true;
		} else if ($sf >0) {
			$settings['superflex'] = true;
		} else {
            $settings["standard"] = true;
        }

        return $settings;
    }

    public function checkIfLeagueIsPPR($scoring_settings): string
    {
        if ( isset( $scoring_settings->rec ) && $scoring_settings->rec >=1) {
			$team_type = 'PPR';
		} elseif( isset( $scoring_settings->rec ) && $scoring_settings->rec >0 && $scoring_settings->rec <1 ) {
			$team_type = 'HALF PPR';
		} else {
			$team_type = 'NON PPR';
		}

        return $team_type;
    }

    public function getRosterPositions($leagueInfo): array
    {
        return $leagueInfo->roster_positions;
    }

    function get_roster_map($league_id) {
		global $current_user;

		$slug = '_'.$this->slug.'_user_'.$current_user->ID.'_league_roster_map_'.$league_id.'';
		$json = get_transient($slug);
		$json = false;
		
		if ($json == false) {
			$json = $this->getTeamRosters($league_id);
			
			$roster_map = array();
			if ( ! empty( $json ) ) {
				foreach($json as $roster){
					$roster_map[$roster->roster_id] = $roster->owner_id;
				}
			}
			
			set_transient( $slug, $roster_map , 6 * HOUR_IN_SECONDS );
		}
			
		return $roster_map;
	}
    
	function ordinal($num) {
		// Special case "teenth"
		if ( ($num / 10) % 10 != 1 ) {
			// Handle 1st, 2nd, 3rd
			switch( $num % 10 ) {
				case 1: return $num . 'st';
				case 2: return $num . 'nd';
				case 3: return $num . 'rd';  
			}
		}

		// Everything else is "nth"
		return $num . 'th';
	}

    



	/****************************************************************************************
	 * 
	 * 
	 *              Sleeper Web API Functions
	 *              (Retrieving the data from Sleeper)
	 * 
	 * 
	 ****************************************************************************************/
    private function makeRequest(string $endpoint)
    {
        $url = $this->api_url . $endpoint;

        $response = wp_remote_get($url);

        if (is_wp_error($response)) {
            // echo "Request failed: " . $response->get_error_message();
            return null;
        }

        $body = json_decode(wp_remote_retrieve_body($response));

        if (json_last_error() !== JSON_ERROR_NONE) {
            // echo "JSON decoding failed: " . json_last_error_msg();
            return null;
        }

        return $body;
    }

    public function getUser(string $username): ?array
    {
        return $this->makeRequest('user/'.$username.'');
    }

    public function getUserById(string $userId)
    {
        return $this->makeRequest('user/'.$userId.'');
    }

    public function getAllLeaguesForUser(string $loginCookie)
    {
        return $this->makeRequest('user/'.$loginCookie.'/leagues/nfl/'.$this->season.'');
    }

    public function getLeagueInfo(string $leagueId)
    {
        return $this->makeRequest('league/'.$leagueId.'');
    }

    public function getDrafts(string $leagueId)
    {
        return $this->makeRequest('league/'.$leagueId.'/drafts');
    }

    public function getTradedDraftPicks(string $leagueId)
    {
        return $this->makeRequest('league/'.$leagueId.'/traded_picks');
    }
    
    public function getDraftResults(string $draftId)
    {
        return $this->makeRequest('draft/'.$draftId);
    }

    public function getTeamRosters(string $leagueId)
    {
        return $this->makeRequest('league/'.$leagueId.'/rosters');
    }

    public function getTeamUsers(string $leagueId)
    {
        return $this->makeRequest('league/'.$leagueId.'/users');
    }
    

}